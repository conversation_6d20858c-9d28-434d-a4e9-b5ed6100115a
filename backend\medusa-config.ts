import { loadEnv, defineConfig } from '@medusajs/framework/utils'

loadEnv(process.env.NODE_ENV || 'development', process.cwd())

module.exports = defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS || "http://localhost:5173,http://localhost:3000",
      adminCors: process.env.ADMIN_CORS || "http://localhost:7001,http://localhost:7000,http://localhost:9000",
      authCors: process.env.AUTH_CORS || "http://localhost:5173,http://localhost:3000,http://localhost:7001",
      jwtSecret: process.env.JWT_SECRET || "supersecret",
      cookieSecret: process.env.COOKIE_SECRET || "supersecret",
    }
  },
  admin: {
    disable: false,
    path: "/app",
    backendUrl: process.env.MEDUSA_BACKEND_URL,
  },
  modules: [
    {
      resolve: "./src/modules/book",
    },
    {
      resolve: "./src/modules/notification",
    },
    {
      resolve: "./src/modules/digital-product",
    },
    {
      resolve: "@medusajs/medusa/stock-location",
    },
    {
      resolve: "@medusajs/medusa/inventory",
    },
    {
      resolve: "@medusajs/medusa/payment",
      options: {
        providers: [
          // Conditionally include Stripe provider
          ...(process.env.PAYMENT_PROVIDER === "stripe" ? [{
            resolve: "@medusajs/medusa/payment-stripe",
            id: "stripe",
            options: {
              apiKey: process.env.STRIPE_API_KEY,
              webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
              capture: true, // Enable automatic capture
            },
          }] : []),
          // Conditionally include Tranzila provider
          ...(process.env.PAYMENT_PROVIDER === "tranzila" ? [{
            resolve: "./src/modules/payment-tranzila",
            id: "tranzila",
            options: {
              supplier: process.env.TRANZILA_SUPPLIER,
              user: process.env.TRANZILA_USER,
              password: process.env.TRANZILA_PASSWORD,
              currency: process.env.TRANZILA_CURRENCY || "ILS",
              language: process.env.TRANZILA_LANGUAGE || "he",
              test_mode: process.env.TRANZILA_TEST_MODE === "true",
              iframe_mode: process.env.TRANZILA_IFRAME_MODE === "true",
            },
          }] : []),
        ],
      },
    },
    {
      resolve: "@medusajs/medusa/fulfillment",
      options: {
        providers: [
          {
            resolve: "@medusajs/medusa/fulfillment-manual",
            id: "manual",
            options: {
              name: "Manual Fulfillment Provider",
            },
          },
          {
            resolve: "./src/modules/fulfillment",
            id: "digital-fulfillment",
            options: {
              name: "Digital Fulfillment Provider",
            },
          },
        ],
      },
    },
    {
      resolve: "@medusajs/medusa/file",
      options: {
        providers: [
          {
            resolve: "@medusajs/medusa/file-local",
            id: "local",
            options: {
              backend_url: process.env.BACKEND_URL || "http://localhost:9000",
              upload_dir: "static",
            },
          },
        ],
      },
    },
    {
      resolve: "@medusajs/medusa/notification",
      options: {
        providers: [
          {
            resolve: "@medusajs/medusa/notification-local",
            id: "local",
            options: {
              name: "Local Notification Provider",
              channels: ["email"],
            },
          },
        ],
      },
    },
  ],
})
