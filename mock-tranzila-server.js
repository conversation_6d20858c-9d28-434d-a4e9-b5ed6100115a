const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static('public'));

// Mock Tranzila payment page
app.get('/cgi-bin/tranzila71u.cgi', (req, res) => {
  const {
    supplier,
    sum,
    currency,
    myid,
    success_url,
    error_url,
    notify_url,
    contact,
    email,
    phone,
    iframe
  } = req.query;

  console.log('🔄 Mock Tranzila Payment Request:', {
    supplier,
    sum,
    currency,
    myid,
    contact,
    email,
    iframe: iframe === '1' ? 'Yes' : 'No'
  });

  // If iframe mode, return a simple payment form
  if (iframe === '1') {
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Mock Tranzila Payment</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
          .payment-form { background: white; padding: 30px; border-radius: 8px; max-width: 400px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .form-group { margin-bottom: 15px; }
          label { display: block; margin-bottom: 5px; font-weight: bold; }
          input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
          button { width: 100%; padding: 12px; background: #007cba; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; }
          button:hover { background: #005a87; }
          .amount { font-size: 18px; font-weight: bold; color: #007cba; text-align: center; margin-bottom: 20px; }
          .test-cards { background: #e8f4f8; padding: 15px; border-radius: 4px; margin-bottom: 20px; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="payment-form">
          <h2>Mock Tranzila Payment</h2>
          <div class="amount">Amount: ${sum} ${currency}</div>
          <div class="test-cards">
            <strong>Test Cards:</strong><br>
            Success: ****************<br>
            Failure: ****************<br>
            Any future expiry date
          </div>
          <form id="paymentForm">
            <div class="form-group">
              <label>Card Number:</label>
              <input type="text" id="cardNumber" placeholder="****************" maxlength="16" required>
            </div>
            <div class="form-group">
              <label>Expiry Date (MM/YY):</label>
              <input type="text" id="expiry" placeholder="12/25" maxlength="5" required>
            </div>
            <div class="form-group">
              <label>CVV:</label>
              <input type="text" id="cvv" placeholder="123" maxlength="3" required>
            </div>
            <div class="form-group">
              <label>Cardholder Name:</label>
              <input type="text" id="cardName" placeholder="John Doe" required>
            </div>
            <button type="submit">Pay ${sum} ${currency}</button>
          </form>
        </div>

        <script>
          document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const cardNumber = document.getElementById('cardNumber').value;
            const isSuccess = cardNumber === '****************';
            
            // Simulate processing delay
            const button = document.querySelector('button');
            button.textContent = 'Processing...';
            button.disabled = true;
            
            setTimeout(() => {
              // Send result to parent window (for iframe mode)
              if (window.parent !== window) {
                window.parent.postMessage({
                  type: isSuccess ? 'tranzila_payment_complete' : 'tranzila_payment_error',
                  result: {
                    success: isSuccess,
                    transactionId: '${myid}',
                    responseCode: isSuccess ? '000' : '001',
                    amount: '${sum}',
                    currency: '${currency}'
                  }
                }, '*');
              }
              
              // Also redirect for non-iframe mode
              const redirectUrl = isSuccess ? '${success_url}' : '${error_url}';
              if (redirectUrl && redirectUrl !== 'undefined') {
                window.location.href = redirectUrl + '?myid=${myid}&Response=' + (isSuccess ? '000' : '001') + '&sum=${sum}&currency=${currency}&ConfirmationCode=' + (isSuccess ? 'MOCK123456' : '') + '&Responsemessage=' + (isSuccess ? 'Success' : 'Card declined');
              }
            }, 2000);
          });
        </script>
      </body>
      </html>
    `);
  } else {
    // Redirect mode - show full page
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Mock Tranzila Payment</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
          .payment-form { background: white; padding: 40px; border-radius: 8px; max-width: 500px; margin: 50px auto; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
          .form-group { margin-bottom: 20px; }
          label { display: block; margin-bottom: 8px; font-weight: bold; }
          input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; font-size: 16px; }
          button { width: 100%; padding: 15px; background: #007cba; color: white; border: none; border-radius: 4px; font-size: 18px; cursor: pointer; }
          button:hover { background: #005a87; }
          .amount { font-size: 24px; font-weight: bold; color: #007cba; text-align: center; margin-bottom: 30px; }
          .test-cards { background: #e8f4f8; padding: 20px; border-radius: 4px; margin-bottom: 30px; }
          .header { text-align: center; margin-bottom: 30px; }
        </style>
      </head>
      <body>
        <div class="payment-form">
          <div class="header">
            <h1>🏦 Mock Tranzila Payment Gateway</h1>
            <div class="amount">Amount: ${sum} ${currency}</div>
          </div>
          
          <div class="test-cards">
            <h3>Test Card Numbers:</h3>
            <p><strong>Success:</strong> ****************</p>
            <p><strong>Failure:</strong> ****************</p>
            <p><strong>Expiry:</strong> Any future date (e.g., 12/25)</p>
            <p><strong>CVV:</strong> Any 3 digits (e.g., 123)</p>
          </div>

          <form id="paymentForm">
            <div class="form-group">
              <label>Card Number:</label>
              <input type="text" id="cardNumber" placeholder="Enter test card number" maxlength="16" required>
            </div>
            <div class="form-group">
              <label>Expiry Date (MM/YY):</label>
              <input type="text" id="expiry" placeholder="12/25" maxlength="5" required>
            </div>
            <div class="form-group">
              <label>CVV:</label>
              <input type="text" id="cvv" placeholder="123" maxlength="3" required>
            </div>
            <div class="form-group">
              <label>Cardholder Name:</label>
              <input type="text" id="cardName" placeholder="John Doe" required>
            </div>
            <button type="submit">Complete Payment</button>
          </form>
        </div>

        <script>
          document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const cardNumber = document.getElementById('cardNumber').value;
            const isSuccess = cardNumber === '****************';
            
            // Simulate processing delay
            const button = document.querySelector('button');
            button.textContent = 'Processing Payment...';
            button.disabled = true;
            
            setTimeout(() => {
              // First, send webhook notification
              fetch('/webhook-notify', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  myid: '${myid}',
                  Response: isSuccess ? '000' : '001',
                  sum: '${sum}',
                  currency: '${currency}',
                  ConfirmationCode: isSuccess ? 'MOCK123456' : '',
                  Responsemessage: isSuccess ? 'Success' : 'Card declined',
                  cardtype: 'Visa',
                  cardnum: '****' + cardNumber.slice(-4),
                  expmonth: '12',
                  expyear: '25',
                  notify_url: '${notify_url}'
                })
              }).then(() => {
                // Then redirect user
                const redirectUrl = isSuccess ? '${success_url}' : '${error_url}';
                const params = new URLSearchParams({
                  myid: '${myid}',
                  Response: isSuccess ? '000' : '001',
                  sum: '${sum}',
                  currency: '${currency}',
                  ConfirmationCode: isSuccess ? 'MOCK123456' : '',
                  Responsemessage: isSuccess ? 'Success' : 'Card declined'
                });
                
                window.location.href = redirectUrl + '?' + params.toString();
              });
            }, 3000);
          });
        </script>
      </body>
      </html>
    `);
  }
});

// Webhook notification endpoint
app.post('/webhook-notify', async (req, res) => {
  const { notify_url, ...webhookData } = req.body;
  
  console.log('🔔 Sending webhook notification to:', notify_url);
  console.log('📦 Webhook data:', webhookData);
  
  if (notify_url && notify_url !== 'undefined') {
    try {
      // Send webhook to the actual backend
      const fetch = require('node-fetch');
      const response = await fetch(notify_url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(webhookData)
      });
      
      console.log('✅ Webhook sent, status:', response.status);
    } catch (error) {
      console.error('❌ Webhook failed:', error.message);
    }
  }
  
  res.json({ status: 'ok' });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'Mock Tranzila Server Running', port: PORT });
});

app.listen(PORT, () => {
  console.log(`🚀 Mock Tranzila Server running on http://localhost:${PORT}`);
  console.log(`📋 Payment endpoint: http://localhost:${PORT}/cgi-bin/tranzila71u.cgi`);
  console.log(`🔔 Webhook endpoint: http://localhost:${PORT}/webhook-notify`);
  console.log(`\n🧪 Test Cards:`);
  console.log(`   Success: ****************`);
  console.log(`   Failure: ****************`);
  console.log(`   Expiry: Any future date`);
  console.log(`   CVV: Any 3 digits\n`);
});
