<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import { cartActions } from '$lib/stores/cart'
  import { formatPriceWithCurrency } from '$lib/stores/currency'

  export let cart: any
  export let onComplete: (result: { success: boolean, order?: any, error?: string }) => void
  export let onBack: () => void

  let isProcessing = false
  let paymentError = ''
  let paymentUrl = ''
  let transactionId = ''
  let useIframe = false
  let iframeLoaded = false

  // Get Tranzila configuration from environment
  const tranzilaIframeMode = import.meta.env.VITE_TRANZILA_IFRAME_MODE === 'true'

  onMount(() => {
    useIframe = tranzilaIframeMode
    generatePaymentUrl()
  })

  async function generatePaymentUrl() {
    try {
      isProcessing = true
      paymentError = ''

      if (!cart?.payment_collection?.id) {
        throw new Error('No payment collection found')
      }

      console.log('🔄 Generating Tranzila payment URL for cart:', cart.id)

      // Generate payment URL via backend API
      const response = await apiClient.generateTranzilaPaymentUrl(cart.payment_collection.id)

      if (response.data?.payment_url) {
        paymentUrl = response.data.payment_url
        transactionId = response.data.transaction_id
        console.log('✅ Tranzila payment URL generated:', paymentUrl)
      } else {
        throw new Error('Failed to generate payment URL')
      }
    } catch (error) {
      console.error('❌ Failed to generate Tranzila payment URL:', error)
      paymentError = error instanceof Error ? error.message : $_('checkout.payment_failed')
    } finally {
      isProcessing = false
    }
  }

  function handleRedirectPayment() {
    if (!paymentUrl) {
      paymentError = $_('checkout.payment_url_not_available')
      return
    }

    console.log('🔄 Redirecting to Tranzila payment page')
    
    // Store cart ID and transaction ID in sessionStorage for return handling
    sessionStorage.setItem('tranzila_cart_id', cart.id)
    sessionStorage.setItem('tranzila_transaction_id', transactionId)
    
    // Redirect to Tranzila payment page
    window.location.href = paymentUrl
  }

  function handleIframeLoad() {
    iframeLoaded = true
    console.log('✅ Tranzila iframe loaded')
  }

  function handleIframeError() {
    console.error('❌ Tranzila iframe failed to load')
    paymentError = $_('checkout.iframe_load_failed')
    iframeLoaded = false
  }

  // Listen for messages from iframe (if using iframe mode)
  function handleMessage(event: MessageEvent) {
    if (!useIframe) return

    // Verify origin for security
    const allowedOrigins = ['https://secure5.tranzila.com', 'https://sandbox.tranzila.com']
    if (!allowedOrigins.includes(event.origin)) {
      console.warn('Received message from unauthorized origin:', event.origin)
      return
    }

    console.log('📨 Received message from Tranzila iframe:', event.data)

    try {
      const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data

      if (data.type === 'tranzila_payment_complete') {
        handlePaymentComplete(data.result)
      } else if (data.type === 'tranzila_payment_error') {
        handlePaymentError(data.error)
      }
    } catch (error) {
      console.error('Error parsing iframe message:', error)
    }
  }

  async function handlePaymentComplete(result: any) {
    try {
      console.log('✅ Tranzila payment completed:', result)
      
      // Complete the cart
      const completeResponse = await apiClient.completeCart(cart.id)
      
      if (completeResponse.data?.type === 'order' && completeResponse.data.order) {
        // Order placed successfully
        console.log('✅ Order placed successfully:', completeResponse.data.order.id)
        cartActions.initialize() // Refresh cart
        onComplete({
          success: true,
          order: completeResponse.data.order
        })
      } else {
        console.error('❌ Cart completion failed')
        const errorMsg = $_('checkout.order_completion_failed')
        onComplete({ success: false, error: errorMsg })
      }
    } catch (error) {
      console.error('❌ Error completing order:', error)
      const errorMsg = error instanceof Error ? error.message : $_('checkout.order_completion_failed')
      onComplete({ success: false, error: errorMsg })
    }
  }

  function handlePaymentError(error: any) {
    console.error('❌ Tranzila payment error:', error)
    paymentError = error.message || $_('checkout.payment_failed')
    onComplete({ success: false, error: paymentError })
  }

  // Add event listener for iframe messages
  onMount(() => {
    if (useIframe) {
      window.addEventListener('message', handleMessage)
      return () => {
        window.removeEventListener('message', handleMessage)
      }
    }
  })
</script>

<svelte:head>
  <title>{$_('checkout.tranzila_payment')} - Hebrew Book Store</title>
</svelte:head>

<div class="tranzila-payment">
  <div class="payment-header">
    <h2>{$_('checkout.complete_payment')}</h2>
    <p class="payment-amount">
      {$_('checkout.total')}: {formatPriceWithCurrency(cart.total, cart.currency_code)}
    </p>
  </div>

  {#if paymentError}
    <div class="error-message">
      <p>{paymentError}</p>
      <button type="button" on:click={generatePaymentUrl} class="retry-button">
        {$_('checkout.retry')}
      </button>
    </div>
  {:else if isProcessing}
    <div class="loading-state">
      <div class="spinner"></div>
      <p>{$_('checkout.preparing_payment')}</p>
    </div>
  {:else if useIframe && paymentUrl}
    <div class="iframe-container">
      <p class="iframe-instructions">
        {$_('checkout.complete_payment_below')}
      </p>
      
      {#if !iframeLoaded}
        <div class="iframe-loading">
          <div class="spinner"></div>
          <p>{$_('checkout.loading_payment_form')}</p>
        </div>
      {/if}
      
      <iframe
        src={paymentUrl}
        title="Tranzila Payment"
        class="payment-iframe"
        class:loaded={iframeLoaded}
        on:load={handleIframeLoad}
        on:error={handleIframeError}
        frameborder="0"
        scrolling="auto"
      ></iframe>
    </div>
  {:else if paymentUrl}
    <div class="redirect-container">
      <p class="redirect-instructions">
        {$_('checkout.redirect_to_payment')}
      </p>
      
      <button 
        type="button" 
        on:click={handleRedirectPayment}
        class="payment-button primary"
        disabled={isProcessing}
      >
        {$_('checkout.proceed_to_payment')}
      </button>
    </div>
  {/if}

  <div class="payment-actions">
    <button type="button" on:click={onBack} class="back-button">
      {$_('checkout.back')}
    </button>
  </div>
</div>

<style>
  .tranzila-payment {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
  }

  .payment-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .payment-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
  }

  .payment-amount {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
  }

  .error-message {
    background: var(--error-bg);
    border: 1px solid var(--error-border);
    color: var(--error-color);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    text-align: center;
  }

  .retry-button {
    background: var(--error-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 0.5rem;
  }

  .loading-state {
    text-align: center;
    padding: 2rem;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .iframe-container {
    margin-bottom: 2rem;
  }

  .iframe-instructions {
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-secondary);
  }

  .iframe-loading {
    text-align: center;
    padding: 2rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }

  .payment-iframe {
    width: 100%;
    min-height: 600px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .payment-iframe.loaded {
    opacity: 1;
  }

  .redirect-container {
    text-align: center;
    padding: 2rem;
  }

  .redirect-instructions {
    margin-bottom: 2rem;
    color: var(--text-secondary);
  }

  .payment-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .payment-button:hover:not(:disabled) {
    background: var(--primary-hover);
  }

  .payment-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .payment-actions {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }

  .back-button {
    background: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .back-button:hover {
    background: var(--hover-bg);
  }
</style>
