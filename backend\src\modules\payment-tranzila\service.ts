import {
  IPaymentProvider,
  PaymentSessionStatus,
} from "@medusajs/framework/types"
import { MedusaError } from "@medusajs/framework/utils"
import crypto from "crypto"

export interface TranzilaOptions {
  supplier: string
  user?: string
  password?: string
  currency?: string
  language?: string
  test_mode?: boolean
  iframe_mode?: boolean
}

export interface TranzilaPaymentData {
  supplier: string
  user?: string
  password?: string
  sum: string
  currency: string
  cred_type: string
  tranmode: string
  myid: string
  npay?: string
  fpay?: string
  spay?: string
  contact?: string
  email?: string
  phone?: string
  address?: string
  city?: string
  zip?: string
  company?: string
  remarks?: string
  lang?: string
  response?: string
  iframe?: string
  success_url?: string
  error_url?: string
  notify_url?: string
}

class TranzilaPaymentProviderService implements IPaymentProvider {
  static identifier = "tranzila"

  protected options_: TranzilaOptions
  private readonly baseUrl = "https://secure5.tranzila.com"
  private readonly testUrl = "http://localhost:3001"  // Mock server for testing

  constructor(container: any, options: TranzilaOptions) {
    this.options_ = {
      currency: "ILS",
      language: "he",
      test_mode: false,
      iframe_mode: false,
      ...options,
    }

    if (!this.options_.supplier) {
      throw new MedusaError(
        MedusaError.Types.INVALID_ARGUMENT,
        "Tranzila supplier is required"
      )
    }
  }

  getIdentifier(): string {
    return "tranzila"
  }

  async getWebhookActionAndData(data: any): Promise<any> {
    return {
      action: "payment_update",
      data: data
    }
  }

  async getPaymentStatus(data: any): Promise<any> {
    const status = data.status as string

    switch (status) {
      case "pending":
        return { status: "pending" }
      case "authorized":
        return { status: "authorized" }
      case "captured":
        return { status: "captured" }
      case "canceled":
        return { status: "canceled" }
      case "error":
        return { status: "error" }
      default:
        return { status: "pending" }
    }
  }

  async initiatePayment(
    context: any
  ): Promise<any> {
    try {
      const { amount, currency_code, context: paymentContext, resource_id } = context
      const { customer, billing_address } = paymentContext

      // Generate unique transaction ID
      const transactionId = `${resource_id}_${Date.now()}`
      
      // Prepare Tranzila payment data
      const paymentData: TranzilaPaymentData = {
        supplier: this.options_.supplier,
        user: this.options_.user,
        password: this.options_.password,
        sum: (amount / 100).toFixed(2), // Convert from cents to currency units
        currency: currency_code?.toUpperCase() || this.options_.currency || "ILS",
        cred_type: "1", // Credit card
        tranmode: "A", // Authorization (can be changed to "AK" for immediate capture)
        myid: transactionId,
        lang: this.options_.language || "he",
        response: "1", // Return response data
      }

      // Add customer information if available
      if (customer?.email) {
        paymentData.email = customer.email
      }
      
      if (billing_address) {
        paymentData.contact = `${billing_address.first_name || ""} ${billing_address.last_name || ""}`.trim()
        paymentData.phone = billing_address.phone || ""
        paymentData.address = billing_address.address_1 || ""
        paymentData.city = billing_address.city || ""
        paymentData.zip = billing_address.postal_code || ""
        paymentData.company = billing_address.company || ""
      }

      // Set URLs for redirect flow
      const baseUrl = process.env.FRONTEND_URL || "http://localhost:5173"
      paymentData.success_url = `${baseUrl}/checkout/tranzila/success`
      paymentData.error_url = `${baseUrl}/checkout/tranzila/error`
      paymentData.notify_url = `${process.env.MEDUSA_BACKEND_URL || "http://localhost:9000"}/webhooks/tranzila`

      // Configure iframe mode if enabled
      if (this.options_.iframe_mode) {
        paymentData.iframe = "1"
      }

      return {
        session_data: {
          id: transactionId,
          status: "pending",
          amount: amount,
          currency: currency_code,
          payment_data: paymentData,
          created_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to initiate Tranzila payment", error)
    }
  }

  async authorizePayment(data: any): Promise<any> {
    try {
      // For Tranzila, authorization happens on their side
      // We just need to verify the payment was successful
      const { payment_data } = data as any

      return {
        session_data: {
          ...data,
          status: "authorized",
          authorized_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to authorize Tranzila payment", error)
    }
  }

  async capturePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      // For immediate capture, we would use tranmode "AK" in initiatePayment
      // For later capture, we would need to make a separate API call to Tranzila
      
      return {
        session_data: {
          ...paymentSessionData,
          status: "captured",
          captured_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to capture Tranzila payment", error)
    }
  }

  async refundPayment(data: any): Promise<any> {
    try {
      // Tranzila refunds would require a separate API call
      // This is a placeholder implementation
      const refundAmount = data.amount || 0

      return {
        session_data: {
          ...data,
          status: "refunded",
          refunded_amount: refundAmount,
          refunded_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to refund Tranzila payment", error)
    }
  }

  async cancelPayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      return {
        session_data: {
          ...paymentSessionData,
          status: "canceled",
          canceled_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to cancel Tranzila payment", error)
    }
  }

  async deletePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      // Clean up any resources if needed
      return
    } catch (error) {
      return this.buildError("Failed to delete Tranzila payment", error)
    }
  }

  async retrievePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      return {
        session_data: paymentSessionData,
      }
    } catch (error) {
      return this.buildError("Failed to retrieve Tranzila payment", error)
    }
  }

  async updatePayment(
    context: any
  ): Promise<any> {
    try {
      const { amount, currency_code, data } = context
      
      return {
        session_data: {
          ...data,
          amount: amount,
          currency: currency_code,
          updated_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to update Tranzila payment", error)
    }
  }

  // Helper method to generate Tranzila payment URL
  generatePaymentUrl(paymentData: TranzilaPaymentData): string {
    const baseUrl = this.options_.test_mode ? this.testUrl : this.baseUrl
    const params = new URLSearchParams()
    
    Object.entries(paymentData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString())
      }
    })
    
    return `${baseUrl}/cgi-bin/tranzila71u.cgi?${params.toString()}`
  }

  // Helper method to verify Tranzila response
  verifyResponse(responseData: Record<string, string>): boolean {
    // Implement response verification logic based on Tranzila documentation
    // This typically involves checking response codes and signatures
    return responseData.Response === "000" // Success response code
  }

  private buildError(message: string, error: any): any {
    return {
      error: message,
      code: "tranzila_error",
      detail: error?.message || error,
    }
  }
}

export default TranzilaPaymentProviderService
