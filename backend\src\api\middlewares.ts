import { defineMiddlewares,
  validateAndTransformBody } from "@medusajs/framework/http"
import { createDigitalProductsSchema } from "./validation-schemas"
import multer from "multer"

const upload = multer({ storage: multer.memoryStorage() })

// CORS middleware for PDF reader and static files
const pdfCorsMiddleware = (req: any, res: any, next: any) => {
  // Allow PDF readers and embedded content
  const origin = req.headers.origin
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'https://hebrewbook.azurewebsites.net', // Production frontend
    'null', // For file:// protocol and PDF readers
    undefined // For same-origin requests
  ]

  if (allowedOrigins.includes(origin) || !origin) {
    res.header('Access-Control-Allow-Origin', origin || '*')
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, x-publishable-api-key')
  res.header('Access-Control-Allow-Credentials', 'true')

  // PDF-specific headers
  res.header('X-Frame-Options', 'SAMEORIGIN')
  res.header('Content-Security-Policy', "frame-ancestors 'self' http://localhost:5173 https://hebrewbook.azurewebsites.net")

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200)
  } else {
    next()
  }
}

export default defineMiddlewares({
  routes: [
    {
      matcher: "/admin/digital-products",
      method: "POST",
      middlewares: [
        validateAndTransformBody(createDigitalProductsSchema),
      ],
    },
    {
      matcher: "/admin/digital-products/upload**",
      method: "POST",
      middlewares: [
        (req, res, next) =>
          upload.array("files")(
            req as any,
            res as any,
            next as any
          ),
      ],
    },
    {
      matcher: "/static/**",
      method: ["GET", "OPTIONS"],
      middlewares: [pdfCorsMiddleware],
    },
    {
      matcher: "/store/customers/me/digital-products/**",
      method: ["GET", "OPTIONS"],
      middlewares: [pdfCorsMiddleware],
    },
    {
      matcher: "/uploads/**",
      method: ["GET", "OPTIONS"],
      middlewares: [pdfCorsMiddleware],
    },
  ],
})
