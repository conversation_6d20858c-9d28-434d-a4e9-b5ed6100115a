import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { MedusaError } from "@medusajs/framework/utils"
import TranzilaPaymentProviderService from "../../../../modules/payment-tranzila/service"

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const body = req.body as { payment_collection_id?: string }
    const { payment_collection_id } = body

    if (!payment_collection_id) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "payment_collection_id is required"
      )
    }

    // Get the payment collection
    const paymentModuleService = req.scope.resolve("paymentModuleService") as any
    const paymentCollection = await paymentModuleService.retrievePaymentCollection(
      payment_collection_id,
      {
        relations: ["payment_sessions"],
      }
    )

    if (!paymentCollection) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        "Payment collection not found"
      )
    }

    // Find the Tranzila payment session
    const tranzilaSession = paymentCollection.payment_sessions?.find(
      (session: any) => session.provider_id === "pp_tranzila_tranzila"
    )

    if (!tranzilaSession) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        "Tranzila payment session not found"
      )
    }

    // Get the Tranzila service
    const tranzilaService = req.scope.resolve("pp_tranzila_tranzila") as TranzilaPaymentProviderService

    // Generate payment URL
    const paymentData = tranzilaSession.data?.payment_data
    if (!paymentData) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Payment data not found in session"
      )
    }

    const paymentUrl = tranzilaService.generatePaymentUrl(paymentData)

    res.json({
      payment_url: paymentUrl,
      session_id: tranzilaSession.id,
      transaction_id: paymentData.myid,
    })
  } catch (error) {
    console.error("Error generating Tranzila payment URL:", error)
    
    if (error instanceof MedusaError) {
      return res.status(error.type === MedusaError.Types.NOT_FOUND ? 404 : 400).json({
        error: error.message,
        code: error.code,
      })
    }

    res.status(500).json({
      error: "Failed to generate payment URL",
      details: error instanceof Error ? error.message : "Unknown error",
    })
  }
}
